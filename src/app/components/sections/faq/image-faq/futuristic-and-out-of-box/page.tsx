'use client'

import React, { Suspense } from 'react'
import { ReactLenis } from 'lenis/react'
import { PageNav } from '@/components/common/PageNav'
import { useSearchParams } from 'next/navigation'
import { ColorTemplate } from '@/components/sections/styles/shared/themeConfig'
import { getFuturisticImageFAQStyle } from '@/components/sections/styles/faq/image/futuristicAndOutOfBox'
import ImageFAQ from '@/components/sections/faq/ImageFAQ'

function FuturisticImageFAQContent() {
  const searchParams = useSearchParams()
  const theme = (Number(searchParams.get('theme')) || 1) as ColorTemplate
  const style = getFuturisticImageFAQStyle(theme)
  
  return <ImageFAQ style={style} />
}

export default function FuturisticImageFAQPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <FuturisticImageFAQContent />
      </Suspense>
    </ReactLenis>
  )
}
