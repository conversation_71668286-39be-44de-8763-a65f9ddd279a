"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { ColorTemplate } from "@/components/sections/styles/shared/themeConfig";
import { getFuturisticBentoFAQStyle } from "@/components/sections/styles/faq/bento/futuristicAndOutOfBox";
import BentoFAQ from "@/components/sections/faq/BentoFAQ";

function FuturisticBentoFAQContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;
  const style = getFuturisticBentoFAQStyle(theme);

  return <BentoFAQ style={style} />;
}

export default function FuturisticBentoFAQPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <FuturisticBentoFAQContent />
      </Suspense>
    </ReactLenis>
  );
}
