"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { ColorTemplate } from "@/components/sections/styles/shared/themeConfig";
import { getFunAndTrendyBentoFAQStyle } from "@/components/sections/styles/faq/bento/funAndTrendy";
import BentoFAQ from "@/components/sections/faq/BentoFAQ";

function FunAndTrendyBentoFAQContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;
  const style = getFunAndTrendyBentoFAQStyle(theme);

  return <BentoFAQ style={style} />;
}

export default function FunAndTrendyBentoFAQPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <FunAndTrendyBentoFAQContent />
      </Suspense>
    </ReactLenis>
  );
}
