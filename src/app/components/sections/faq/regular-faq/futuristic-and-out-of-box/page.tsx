"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { ColorTemplate } from "@/components/sections/styles/shared/themeConfig";
import { getFuturisticRegularFAQStyle } from "@/components/sections/styles/faq/regular/futuristicAndOutOfBox";
import RegularFAQ from "@/components/sections/faq/RegularFAQ";

function FuturisticRegularFAQContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;
  const style = getFuturisticRegularFAQStyle(theme);

  return <RegularFAQ style={style} />;
}

export default function FuturisticRegularFAQPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <FuturisticRegularFAQContent />
      </Suspense>
    </ReactLenis>
  );
}
