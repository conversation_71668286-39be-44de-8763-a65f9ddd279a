import { FAQStyle } from "../types";
import { funAndTrendyTheme as theme } from "../../shared/themes";
import { ColorTemplate } from "../../shared/themeConfig";
import { getFunAndTrendyColors } from "../../shared/themeConfig";
import { defaultFAQItems } from "../../../faq/constants";

export function getFunAndTrendyRegularFAQStyle(
  colorTemplate: ColorTemplate = 1
): FAQStyle {
  const colors = getFunAndTrendyColors(colorTemplate);

  return {
    section: {
      className: `${theme.spacing.sectionPadding} relative`,
      backgroundColor: colors.primary,
      backgroundPattern: theme.backgrounds.texture,
      showBorder: true,
    },
    title: {
      text: "F.A.Q.",
      className: `${theme.heading.sizes.large} ${theme.text.headingClass} ${theme.heading.className} ${theme.text.white} mb-7`,
      useRetroText: true,
      shadowColor: theme.shadows.retro.color,
      shadowOffset: theme.shadows.retro.offset,
      animation: "slide",
      animationProps: {
        duration: theme.animations.duration,
        stagger: theme.animations.stagger,
        start: "top 80%",
        end: "top 20%",
        variant: theme.animations.variant,
      },
    },
    description: {
      text: "Find answers to common questions about our platform and services.",
      className: `${theme.description.className} ${theme.text.white} max-w-2xl mx-auto`,
    },
    accordion: {
      items: defaultFAQItems,
      className: "max-w-4xl mx-auto",
      itemClassName: `${theme.borders.button} bg-white hover:shadow-[4px_4px_0_#000] transition-all duration-200`,
      itemTitleClassName: `${theme.text.bodyClass} ${theme.heading.className} text-black`,
      itemIconContainerClassName: `${colors.button} ${theme.borders.button}`,
      itemIconClassName: "text-white",
      itemContentClassName: `${theme.fonts.body.className} text-base md:text-lg text-black/80 tracking-tight uppercase font-semibold`,
    },
  };
}

export const funAndTrendyRegularFAQStyle = getFunAndTrendyRegularFAQStyle(1);
