import React, { memo } from "react";
import BentoAccordion from "@/components/accordions/BentoAccordion";
import TextRenderer from "@/components/sections/layouts/TextRenderer";
import { BentoFAQStyle } from "../styles/faq/types";
import { cls } from "@/lib/utils";

interface BentoFAQProps {
  style: BentoFAQStyle;
}

const BentoFAQ = memo(function BentoFAQ({ style }: BentoFAQProps) {
  return (
    <section
      className={cls(
        "min-h-screen flex items-center justify-center",
        style.section.className,
        style.section.backgroundColor
      )}
    >
      {/* Gradient Overlay */}
      {style.gradient?.show && (
        <div
          className={cls(
            "absolute pointer-events-none",
            style.gradient.inset || "inset-0",
            style.gradient.rounded || "rounded-none",
            style.gradient.className
          )}
          style={{
            background: `
                            radial-gradient(circle at center, ${
                              style.gradient.radialColor || "transparent"
                            } ${
              style.gradient.radialOpacity || "0%"
            }, transparent 70%),
                            linear-gradient(180deg, transparent 0%, ${
                              style.gradient.linearColor || "transparent"
                            } ${style.gradient.linearOpacity || "0%"})
                        `,
          }}
        />
      )}

      <div
        className={cls("relative z-10 w-full", style.section.innerClassName)}
      >
        {/* Title */}
        <div className="text-center mb-8">
          <TextRenderer config={style.title} as="h1" />

          {style.description && (
            <p className={style.description.className}>
              {style.description.text}
            </p>
          )}
        </div>

        {/* FAQ Bento Accordion */}
        <BentoAccordion
          items={style.accordion.items}
          title=""
          className={style.accordion.className}
          titleClassName="hidden"
          containerClassName={style.bento.containerClassName}
          gridClassName={style.bento.gridClassName}
          itemClassName={style.accordion.itemClassName}
          itemTitleClassName={style.accordion.itemTitleClassName}
          itemIconContainerClassName={
            style.accordion.itemIconContainerClassName
          }
          itemIconClassName={style.accordion.itemIconClassName}
          itemContentClassName={style.accordion.itemContentClassName}
        />
      </div>
    </section>
  );
});

export default BentoFAQ;
